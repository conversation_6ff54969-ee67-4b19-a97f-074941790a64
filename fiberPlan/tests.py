from django.test import TestCase
from django.urls import reverse
from django.contrib.auth.models import User
from .models import Customer, CostQuest, Vault

class FiberPlanUrlTests(TestCase):
    def setUp(self):
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpassword'
        )
    
    def test_vaults_view(self):
        """Test that the vaults page loads correctly"""
        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('fiberPlan:vaults'))
        self.assertEqual(response.status_code, 200)
    
    def test_map_view(self):
        """Test that the map page loads correctly"""
        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('fiberPlan:map'))
        self.assertEqual(response.status_code, 200)

class VaultModelTests(TestCase):
    def test_vault_creation(self):
        """Test creating a basic vault"""
        vault = Vault.objects.create(
            name="Test Vault",
            lat=35.123456,  # Changed from latitude
            lng=-78.654321,  # Changed from longitude
            address="123 Test St",
            is_mini=False,
            is_enc=True,
            notes="Test notes"
        )
        self.assertEqual(vault.name, "Test Vault")
        self.assertEqual(vault.address, "123 Test St")
